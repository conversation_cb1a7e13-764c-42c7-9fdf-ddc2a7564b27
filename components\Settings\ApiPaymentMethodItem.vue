<template>
  <div class="group">
    <!-- Payment Method Row -->
    <div class="flex items-center justify-between p-4 hover:bg-gray-50">
      <!-- Left Side: Toggle + Icon + Name -->
      <div class="flex items-center gap-3">
        <!-- Toggle Switch -->
        <ToggleSwitch
          :model-value="true"
          size="md"
          @change="handleMethodToggle"
        />

        <!-- Method Icon -->
        <div
          class="w-8 h-8 rounded-lg flex items-center justify-center bg-gray-100"
        >
          <img
            v-if="method.image"
            :src="method.image"
            :alt="method.name"
            class="w-6 h-6 object-contain rounded"
          />
          <svg
            v-else
            class="w-6 h-6 text-gray-600"
            fill="currentColor"
            viewBox="0 0 20 20"
          >
            <path
              d="M4 4a2 2 0 00-2 2v1h16V6a2 2 0 00-2-2H4zM18 9H2v5a2 2 0 002 2h12a2 2 0 002-2V9zM4 13a1 1 0 011-1h1a1 1 0 110 2H5a1 1 0 01-1-1zm5-1a1 1 0 100 2h1a1 1 0 100-2H9z"
            />
          </svg>
        </div>

        <!-- Method Name -->
        <div>
          <div class="flex items-center gap-2">
            <span class="text-sm font-medium text-gray-900">
              {{ method.name }}
            </span>
            <span class="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
              {{ method.code }}
            </span>
          </div>
          <p class="text-xs text-gray-500">{{ method.description }}</p>
        </div>
      </div>

      <!-- Right Side: Actions -->
      <div class="flex items-center gap-3">
        <!-- Expand/Collapse Button -->
        <button
          @click="toggleExpanded"
          class="p-1 text-gray-400 hover:text-gray-600"
        >
          <svg
            class="w-4 h-4 transition-transform duration-200"
            :class="{ 'rotate-90': isExpanded }"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M9 5l7 7-7 7"
            />
          </svg>
        </button>

        <!-- More Actions -->
        <div class="relative">
          <button
            @click="toggleMoreActions"
            class="p-1 text-gray-400 hover:text-gray-600"
          >
            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path
                d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z"
              />
            </svg>
          </button>

          <!-- Dropdown Menu -->
          <div
            v-if="isDropdownOpen"
            class="absolute right-0 top-full mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50"
          >
            <button
              @click="openCreateGatewayPopup"
              class="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors duration-200"
            >
              Tạo cổng thanh toán
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Expanded Titles (Beneficiary Accounts) -->
    <div
      v-if="isExpanded && dataPaymentGateway.length > 0"
      class="bg-gray-50 border-t border-gray-200"
    >
      <PaymentMethodTitle
        v-for="paymentGateway in dataPaymentGateway"
        :key="paymentGateway.id"
        :paymentGateway="paymentGateway"
        :method-id="method.id"
        @toggle="handleTitleToggle"
        @edit="handleTitleEdit"
      />
    </div>

    <!-- No Titles Message -->
    <div
      v-else-if="
        isExpanded &&
        (!dataPaymentGateway || dataPaymentGateway.length.length === 0)
      "
      class="bg-gray-50 border-t border-gray-200 px-4 py-3 ml-14"
    >
      <p class="text-xs text-gray-500 italic">Không có tài khoản thụ hưởng</p>
    </div>
  </div>

  <!-- Create Gateway Popup Component -->
  <CreateGatewayPopup
    v-if="showCreateGatewayPopup"
    :method="method"
    @close="closeCreateGatewayPopup"
    @submit="handleFormSubmit"
  />
</template>

<script setup lang="ts">
import CreateGatewayPopup from "./CreateGatewayPopup.vue";

interface BeneficiaryTitle {
  __typename?: string;
  id: string;
  code?: string;
  name: string;
  lang?: string;
  showField?: boolean;
  required?: boolean;
  accountNumber?: string;
  accountName?: string;
  bankName?: string;
  bankCode?: string;
  isActive?: boolean;
  isDefault?: boolean;
}

interface ApiPaymentMethod {
  code: string;
  description: string;
  id: string;
  image: string | null;
  name: string;
  titles: BeneficiaryTitle[];
}

interface Props {
  method: ApiPaymentMethod;
  isExpanded: boolean;
}

interface Emits {
  (e: "toggle-expanded"): void;
  (e: "method-toggle", value: boolean): void;
  (e: "title-toggle", titleId: string, value: boolean): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const toggleExpanded = () => {
  emit("toggle-expanded");
};

const handleMethodToggle = (value: boolean) => {
  emit("method-toggle", value);
};

const handleTitleToggle = async (titleId: string, value: boolean) => {
  await handleGetPaymentGateWwayByMethodCode(props.method.code);
};
const { handleUpdateGatewayConfig } = usePayment();
const handleTitleEdit = async (data: any) => {
  await handleUpdateGatewayConfig(data, "");
  await handleGetPaymentGateWwayByMethodCode(props.method.code);
};

// Payment Gateway Management
const { getPaymentGatewaysByMethodCode } = usePayment();
const dataPaymentGateway = ref<any>([]);

// Dropdown Management
const {
  toggleDropdown,
  isDropdownOpen: checkDropdownOpen,
  closeDropdown,
} = useDropdownManager();
const dropdownId = `more-actions-${props.method.id}`;
const showCreateGatewayPopup = ref(false);

// Computed property to check if this dropdown is open
const isDropdownOpen = computed(() => checkDropdownOpen(dropdownId));

const toggleMoreActions = () => {
  toggleDropdown(dropdownId);
};

const openCreateGatewayPopup = () => {
  closeDropdown();
  showCreateGatewayPopup.value = true;
};

const closeCreateGatewayPopup = () => {
  showCreateGatewayPopup.value = false;
};
const { handleCreateGatewayConfig } = usePayment();
const handleFormSubmit = async (formData: Record<string, string>) => {
  console.log("formData", formData);
  const data = {
    methodCode: props.method?.code,
    subMethodCode: formData?.submethod || "PAY",
    paymentRecordingMethod: formData.paymentRecordingMethod || "",
    gwPartnerCode: formData?.gwPartnerCode || "",
    gwPartnerName: formData?.gwPartnerName || "",
    gwSubChannel: formData?.gwSubChannel || "",
    gwMethodVersion: formData?.gwMethodVersion || "",
    hashAlgorithm: formData?.hashAlgorithm || "",
    accessKey: formData?.accessKey || "",
    secretKey: formData?.secretKey || "",
    requestUrl: formData?.requestUrl || "",
  };
  await handleCreateGatewayConfig(data, formData?.cassoApiKey || "");
  await handleGetPaymentGateWwayByMethodCode(props.method.code);
  showCreateGatewayPopup.value = false;
};

// Close dropdown when clicking outside
const handleClickOutside = (event: Event) => {
  const target = event.target as HTMLElement;
  if (!target.closest(".relative")) {
    closeDropdown();
  }
};

const handleGetPaymentGateWwayByMethodCode = async (code: string) => {
  try {
    console.log("Calling getPaymentGatewaysByMethodCode with code:", code);
    const response = await getPaymentGatewaysByMethodCode(code);
    console.log("Response from getPaymentGatewaysByMethodCode:", response);
    dataPaymentGateway.value = response;
  } catch (error) {
    console.error("Error in handleGetPaymentGateWwayByMethodCode:", error);
    throw error;
  }
};

onMounted(async () => {
  await handleGetPaymentGateWwayByMethodCode(props.method.code);
  document.addEventListener("click", handleClickOutside);
});

onUnmounted(() => {
  document.removeEventListener("click", handleClickOutside);
});
</script>
